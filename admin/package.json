{"name": "vue-project", "version": "0.0.0", "license": "MIT", "scripts": {"dev": "vite", "preview": "vite preview --port 4173", "build": "vite build && node scripts/release.mjs", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@highlightjs/vue-plugin": "^2.1.0", "@vue/shared": "^3.4.38", "@vueuse/core": "^11.0.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^0.27.2", "css-color-function": "^1.3.3", "echarts": "^5.5.1", "element-plus": "2.2.27", "highlight.js": "^11.10.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.2.2", "vue": "^3.4.38", "vue-clipboard3": "^2.0.0", "vue-echarts": "^6.7.3", "vue-router": "^4.4.3", "vue3-video-play": "1.3.1-beta.6", "vuedraggable": "^4.1.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.10.4", "@types/lodash-es": "^4.17.12", "@types/node": "^16.18.105", "@types/nprogress": "^0.2.3", "@vitejs/plugin-legacy": "^2.3.1", "@vitejs/plugin-vue": "^3.2.0", "@vitejs/plugin-vue-jsx": "^2.1.1", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.20", "consola": "^2.15.3", "eslint": "^8.57.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-vue": "^9.27.0", "execa": "^6.1.0", "fs-extra": "^10.1.0", "postcss": "^8.4.41", "prettier": "^2.8.8", "sass": "^1.77.8", "tailwindcss": "^3.4.10", "terser": "^5.31.6", "typescript": "~4.7.4", "unplugin-auto-import": "^0.9.5", "unplugin-vue-components": "^0.19.9", "vite": "^3.2.10", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-tsc": "^0.38.9"}}