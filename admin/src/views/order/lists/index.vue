<template>
    <div>
        <el-card class="!border-none" shadow="never">
            <el-form ref="formRef" class="mb-[-16px]" :model="queryParams" inline>
                <el-form-item label="订单号" prop="orderNo">
                    <el-input
                        v-model="queryParams.orderNo"
                        class="w-[280px]"
                        clearable
                        placeholder="请输入订单号"
                    />
                </el-form-item>
                <el-form-item label="订单状态" prop="orderStatus">
                    <el-select v-model="queryParams.orderStatus" class="w-[280px]" clearable>
                        <el-option label="全部" value="" />
                        <el-option label="待支付" value="1" />
                        <el-option label="已支付" value="2" />
                        <el-option label="已完成" value="3" />
                        <el-option label="已取消" value="4" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                    <el-button
                        type="success"
                        :loading="syncLoading"
                        :disabled="syncLoading"
                        @click="handleSync"
                    >
                        {{ syncLoading ? '同步中...' : '同步订单' }}
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="!border-none mt-4" shadow="never">
            <div>
                <el-button
                    v-perms="['order.order/lists']"
                    :type="isTodayFilter ? 'primary' : 'default'"
                    @click="handleFilterToday"
                >
                    今日订单
                </el-button>
                <el-button
                    v-perms="['order.order/lists']"
                    :type="isYesterdayFilter ? 'primary' : 'default'"
                    @click="handleFilterYesterday"
                >
                    昨日订单
                </el-button>
            </div>
            <el-table v-loading="pager.loading" class="mt-4" :data="pager.lists">
                <el-table-column label="订单号" prop="order_no" min-width="120">
                    <template #default="{ row }">
                        <el-button type="primary" link @click="handleShowDetail(row.id)">
                            {{ row.order_no }}
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column label="渠道流水号" prop="poi_seq" min-width="80">
                    <template #default="{ row }">
                        <div style="text-align: left; padding: 4px 0px">
                            <el-image
                                src="https://s3plus-bj02.sankuai.com/dap-public/%E9%97%AA%E8%B4%AD%403x.png"
                                style="
                                    width: 18px;
                                    height: 18px;
                                    vertical-align: text-bottom;
                                    margin-right: 4px;
                                "
                            />
                            <span>{{ row.poi_seq }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="订单状态" prop="order_status_text" min-width="80" />
                <el-table-column label="用户实付(元)" prop="pay_amount" min-width="80">
                    <template #default="{ row }"> ¥{{ row.pay_amount_yuan }}</template>
                </el-table-column>
                <el-table-column label="商家实收(元)" prop="merchant_income" min-width="80">
                    <template #default="{ row }"> ¥{{ row.merchant_income_yuan }}</template>
                </el-table-column>
                <el-table-column label="用户信息" min-width="180">
                    <template #default="{ row }">
                        {{ row.recipient_name }}<br />
                        {{ row.recipient_phone }}<br />
                        {{ row.recipient_address }}
                    </template>
                </el-table-column>
                <el-table-column label="客户类型" prop="recipient_type_text" min-width="80" />
                <el-table-column label="订单类型" prop="order_type" min-width="80" />
                <el-table-column label="订单类型" prop="order_type_text" min-width="80" />
                <el-table-column label="下单时间" prop="order_time" min-width="100" />
                <el-table-column label="操作" width="200" fixed="right">
                    <template #default="{ row }">
                        <div
                            style="
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                            "
                        >
                            <el-button
                                v-if="row.order_type == 0"
                                v-perms="['order.order/edit', 'order.order/add:edit']"
                                type="primary"
                                link
                                @click="handleSetOrderType(row.id, -1)"
                            >
                                设为虚拟单
                            </el-button>
                            <span v-if="row.order_type == 0">|</span>
                            <el-button
                                v-if="row.order_type == 0"
                                v-perms="['order.order/edit', 'order.order/add:edit']"
                                type="primary"
                                link
                                @click="handleSetOrderType(row.id, 1)"
                            >
                                设为真实单
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="flex justify-end mt-4">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>

        <!-- 订单详情抽屉子组件 -->
        <OrderDetail v-model:visible="drawerVisible" :order-id="currentOrderId" />
    </div>
</template>

<script lang="ts" setup name="orderLists">
import { defineAsyncComponent } from 'vue'
import dayjs from 'dayjs'

import { orderLists, orderSync, orderTypeUpdate, orderSyncStatus } from '@/api/order'
import feedback from '@/utils/feedback'
import { usePaging } from '@/hooks/usePaging'
import config from '@/config'
import sseRequest from '@/utils/request/sseRequest'

const OrderDetail = defineAsyncComponent(() => import('./components/order-detail.vue'))

const queryParams = reactive({
    orderNo: '',
    orderStatus: '',
    startTime: '',
    endTime: ''
})

const isTodayFilter = ref(false)
const isYesterdayFilter = ref(false)

// 抽屉相关状态（简化）
const drawerVisible = ref(false)
const currentOrderId = ref<number | null>(null)

// 同步状态
const syncLoading = ref(false)

const { pager, getLists, resetPage, resetParams } = usePaging({
    fetchFun: orderLists,
    params: queryParams
})

const handleSync = async () => {
    try {
        syncLoading.value = true

        // 启动SSE监听同步状态
        const ssePromise = orderSyncStatus({
            onMessage: (data) => {
                if (data.status === 'syncCompleted') {
                    syncLoading.value = false
                    getLists() // 刷新列表
                }
            },
            onError: () => {
                syncLoading.value = false
            }
        })

        // 发起同步请求
        // await orderSync()

        // 等待SSE完成
        await ssePromise
    } catch (error) {
        console.log(error)
        feedback.msgError('启动同步失败')
        syncLoading.value = false
    }
}

const handleSetOrderType = async (id: number, type: number) => {
    try {
        await feedback.confirm(`确定要设为${type === -1 ? '虚拟单' : '真实单'}？`)
        await orderTypeUpdate({ id, type })
        feedback.msgSuccess('订单类型更新成功')
        getLists()
    } catch (error) {
        feedback.msgError('订单类型更新失败')
    }
}

const handleShowDetail = (orderId: number) => {
    router.push({
        path: '/order/detail',
        query: { id: orderId }
    })
}

onActivated(() => {
    getLists()
})

getLists()

// 处理今日订单筛选
const handleFilterToday = () => {
    isTodayFilter.value = !isTodayFilter.value
    isYesterdayFilter.value = false
    if (isTodayFilter.value) {
        // 设置今天的开始和结束时间
        const today = dayjs()
        queryParams.startTime = today.startOf('day').format('YYYY-MM-DD HH:mm:ss')
        queryParams.endTime = today.endOf('day').format('YYYY-MM-DD HH:mm:ss')
        // 触发查询
        getLists()
    } else {
        // 取消筛选时只清除时间条件，不触发查询
        queryParams.startTime = ''
        queryParams.endTime = ''
        getLists()
    }
}

// 处理昨日订单筛选
const handleFilterYesterday = () => {
    isYesterdayFilter.value = !isYesterdayFilter.value
    isTodayFilter.value = false
    if (isYesterdayFilter.value) {
        // 设置今天的开始和结束时间
        const yesterday = dayjs()
        queryParams.startTime = yesterday
            .startOf('day')
            .subtract(1, 'day')
            .format('YYYY-MM-DD HH:mm:ss')
        queryParams.endTime = yesterday
            .endOf('day')
            .subtract(1, 'day')
            .format('YYYY-MM-DD HH:mm:ss')
        // 触发查询
        getLists()
    } else {
        // 取消筛选时只清除时间条件，不触发查询
        queryParams.startTime = ''
        queryParams.endTime = ''
        getLists()
    }
}
</script>
