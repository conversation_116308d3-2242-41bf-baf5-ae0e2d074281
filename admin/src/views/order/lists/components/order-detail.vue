<template>
    <el-drawer
        v-model="drawerVisible"
        title="订单详情"
        direction="rtl"
        size="60%"
        :before-close="handleClose"
    >
        <div v-loading="loading" class="p-4">
            <el-descriptions v-if="orderData" :column="3" border>
                <el-descriptions-item label="订单号">
                    {{ orderData.order_no }}
                </el-descriptions-item>
                <el-descriptions-item label="订单状态">
                    {{ orderData.order_status_text }}
                </el-descriptions-item>
                <el-descriptions-item label="订单金额">
                    ¥{{ orderData.total_amount_yuan }}
                </el-descriptions-item>
                <el-descriptions-item label="实付金额">
                    ¥{{ orderData.pay_amount_yuan }}
                </el-descriptions-item>
                <el-descriptions-item label="用户信息">
                    {{ orderData.recipient_name }}
                </el-descriptions-item>
                <el-descriptions-item label="联系电话">
                    {{ orderData.recipient_phone }}
                </el-descriptions-item>
                <el-descriptions-item label="收货地址">
                    {{ orderData.recipient_address }}
                </el-descriptions-item>
                <el-descriptions-item label="下单时间">
                    {{ orderData.order_time }}
                </el-descriptions-item>
                <el-descriptions-item label="订单类型">
                    {{ orderData.order_type_text }}
                </el-descriptions-item>
            </el-descriptions>

            <!-- 商品列表 -->
            <el-card class="mt-4" header="商品列表" shadow="never">
                <el-table :data="orderData?.order_items || []" border>
                    <el-table-column label="商品图片" width="100">
                        <template #default="{ row }">
                            <el-image
                                :src="row.product_image"
                                fit="cover"
                                style="width: 60px; height: 60px"
                            />
                        </template>
                    </el-table-column>
                    <el-table-column label="商品名称" prop="product_name" min-width="200" />
                    <el-table-column label="规格" prop="product_specs" min-width="120" />
                    <el-table-column label="单价" prop="sell_price_yuan" width="100">
                        <template #default="{ row }"> ¥{{ row.sell_price_yuan }} </template>
                    </el-table-column>
                    <el-table-column label="数量" prop="buy_quantity" width="80" />
                    <el-table-column label="小计" width="100">
                        <template #default="{ row }"> ¥{{ row.total_amount_yuan }} </template>
                    </el-table-column>
                    <el-table-column label="实付" prop="total_amount_yuan" width="100">
                        <template #default="{ row }"> ¥{{ row.total_amount_yuan }} </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
    </el-drawer>
</template>

<script lang="ts" setup>
import { orderDetail } from '@/api/order'
import feedback from '@/utils/feedback'

defineOptions({
    name: 'OrderDetail'
})

interface Props {
    visible: boolean
    orderId: number | null
}

interface Emits {
    (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const loading = ref(false)
const orderData = ref<any>(null)

// 计算属性处理 v-model
const drawerVisible = computed({
    get: () => props.visible,
    set: (value: boolean) => emit('update:visible', value)
})

// 监听显示状态变化
watch(
    () => props.visible,
    (newVisible) => {
        if (newVisible && props.orderId) {
            getOrderDetail(props.orderId)
        } else if (!newVisible) {
            orderData.value = null
        }
    }
)

// 获取订单详情
const getOrderDetail = async (orderId: number) => {
    try {
        loading.value = true
        orderData.value = await orderDetail({ id: orderId })
    } catch (error) {
        feedback.msgError('获取订单详情失败')
    } finally {
        loading.value = false
    }
}

// 关闭抽屉
const handleClose = (done: () => void) => {
    emit('update:visible', false)
    orderData.value = null
    done()
}
</script>
