<template>
  <el-button 
    :loading="syncLoading" 
    @click="syncOrders"
    type="primary">
    {{ syncLoading ? '同步中...' : '同步订单' }}
  </el-button>
</template>

<script>
export default {
  data() {
    return {
      syncLoading: false,
      eventSource: null
    }
  },
  methods: {
    async syncOrders() {
      this.syncLoading = true;
      
      try {
        // 启动SSE监听
        this.startSSEListener();
        
        // 发起同步请求
        await this.$api.order.sync();
        
      } catch (error) {
        this.$message.error('启动同步失败: ' + error.message);
        this.syncLoading = false;
      }
    },
    
    startSSEListener() {
      this.eventSource = new EventSource('/adminapi/order.sync/status');
      
      this.eventSource.addEventListener('syncCompleted', (event) => {
        this.$message.success(event.data);
        this.syncLoading = false;
        this.eventSource.close();
      });
      
      this.eventSource.addEventListener('syncFailed', (event) => {
        this.$message.error(event.data);
        this.syncLoading = false;
        this.eventSource.close();
      });
      
      this.eventSource.onerror = () => {
        this.$message.error('连接断开');
        this.syncLoading = false;
        this.eventSource.close();
      };
    }
  },
  
  beforeDestroy() {
    if (this.eventSource) {
      this.eventSource.close();
    }
  }
}
</script>