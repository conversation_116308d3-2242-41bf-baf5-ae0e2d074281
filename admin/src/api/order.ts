import request from '@/utils/request'
import sseRequest from '@/utils/request/sseRequest'

// 订单列表
export function orderLists(params?: Record<string, any>) {
    return request.get({ url: '/order.order/lists', params })
}

// 订单详情
export function orderDetail(params: { id: number }) {
    return request.get({ url: '/order.order/detail', params })
}

// 同步订单
export function orderSync() {
    return request.post({ url: '/order.order/sync' })
}

// 订单同步状态监听
export function orderSyncStatus(options?: any) {
    return sseRequest.syncStatus('order.order/status', options)
}

// 更新订单类型
export function orderTypeUpdate(params: any) {
    return request.post({ url: '/order.order/updateType', params })
}
