import { Sse, type SseOptions } from './sse'

export interface SseRequestConfig extends SseOptions {
    url: string
    timeout?: number
}

export class SseRequest {
    private sseInstance: Sse
    private timeoutId: number | null = null

    constructor() {
        this.sseInstance = new Sse()
    }

    request(config: SseRequestConfig): Promise<any> {
        const { url, timeout = 30000, ...options } = config

        return new Promise((resolve, reject) => {
            let isResolved = false

            const cleanup = () => {
                if (this.timeoutId) {
                    clearTimeout(this.timeoutId)
                    this.timeoutId = null
                }
                this.sseInstance.close()
            }

            const safeResolve = (value: any) => {
                if (!isResolved) {
                    isResolved = true
                    cleanup()
                    resolve(value)
                }
            }

            const safeReject = (error: any) => {
                if (!isResolved) {
                    isResolved = true
                    cleanup()
                    reject(error)
                }
            }

            // 设置超时
            if (timeout > 0) {
                this.timeoutId = window.setTimeout(() => {
                    safeReject(new Error('SSE请求超时'))
                }, timeout)
            }

            this.sseInstance
                .start(url, {
                    ...options,
                    onMessage: (data) => {
                        // 清除超时
                        if (this.timeoutId) {
                            clearTimeout(this.timeoutId)
                            this.timeoutId = null
                        }

                        if (options.onMessage) {
                            options.onMessage(data)
                        }
                    },
                    onError: (error) => {
                        if (options.onError) {
                            options.onError(error)
                        }
                        // 错误时自动关闭并拒绝Promise
                        safeReject(error)
                    }
                })
                .then(safeResolve)
                .catch(safeReject)
        })
    }

    // 便捷方法：监听同步状态
    syncStatus(url: string, options?: Omit<SseOptions, 'successMsg' | 'failMsg'>): Promise<any> {
        // 先销毁当前实例，确保完全清理
        this.destroy()

        // 创建新的SSE实例
        this.sseInstance = new Sse()

        return this.request({
            url,
            successMsg: '同步完成',
            failMsg: '同步失败',
            ...options
        })
    }

    // 便捷方法：监听任务状态
    taskStatus(url: string, options?: SseOptions): Promise<any> {
        return this.request({
            url,
            successMsg: '任务完成',
            failMsg: '任务失败',
            ...options
        })
    }

    // 关闭连接
    close() {
        if (this.timeoutId) {
            clearTimeout(this.timeoutId)
            this.timeoutId = null
        }
        this.sseInstance.close()
    }

    // 销毁实例（用于组件卸载时清理）
    destroy() {
        this.close()
        // 强制关闭，确保连接完全断开
        this.sseInstance.forceClose()
    }

    // 获取连接状态
    get isConnected() {
        return this.sseInstance.isConnected
    }

    get isClosed() {
        return this.sseInstance.isClosed
    }

    // 创建全新的实例（用于确保完全隔离）
    static createFresh(): SseRequest {
        return new SseRequest()
    }
}

// 创建默认实例
const sseRequest = new SseRequest()

export default sseRequest
