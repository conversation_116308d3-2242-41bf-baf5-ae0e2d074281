import { Sse, type SseOptions } from './sse'

export interface SseRequestConfig extends SseOptions {
    url: string
    timeout?: number
}

export class SseRequest {
    private sseInstance: Sse
    private timeoutId: number | null = null

    constructor() {
        this.sseInstance = new Sse()
    }

    request(config: SseRequestConfig): Promise<any> {
        const { url, timeout = 30000, ...options } = config

        return new Promise((resolve, reject) => {
            // 设置超时
            if (timeout > 0) {
                this.timeoutId = window.setTimeout(() => {
                    this.sseInstance.close()
                    reject(new Error('SSE请求超时'))
                }, timeout)
            }

            this.sseInstance
                .start(url, {
                    ...options,
                    onMessage: (data) => {
                        // 清除超时
                        if (this.timeoutId) {
                            clearTimeout(this.timeoutId)
                            this.timeoutId = null
                        }

                        if (options.onMessage) {
                            options.onMessage(data)
                        }
                    },
                    onError: (error) => {
                        // 清除超时
                        if (this.timeoutId) {
                            clearTimeout(this.timeoutId)
                            this.timeoutId = null
                        }

                        if (options.onError) {
                            options.onError(error)
                        }
                    }
                })
                .then(resolve)
                .catch(reject)
        })
    }

    // 便捷方法：监听同步状态
    syncStatus(url: string, options?: Omit<SseOptions, 'successMsg' | 'failMsg'>): Promise<any> {
        return this.request({
            url,
            successMsg: '同步完成',
            failMsg: '同步失败',
            ...options
        })
    }

    // 便捷方法：监听任务状态
    taskStatus(url: string, options?: SseOptions): Promise<any> {
        return this.request({
            url,
            successMsg: '任务完成',
            failMsg: '任务失败',
            ...options
        })
    }

    // 关闭连接
    close() {
        if (this.timeoutId) {
            clearTimeout(this.timeoutId)
            this.timeoutId = null
        }
        this.sseInstance.close()
    }
}

// 创建默认实例
const sseRequest = new SseRequest()

export default sseRequest
