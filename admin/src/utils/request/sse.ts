import config from '@/config'
import feedback from '@/utils/feedback'

const baseUrl = config.baseUrl + config.urlPrefix

export interface SseOptions {
    successMsg?: string
    failMsg?: string
    onMessage?: (data: any) => void
    onError?: (error: Event) => void
    onOpen?: (event: Event) => void
}

export class Sse {
    private es: EventSource | null = null
    private currentResolve: ((value: any) => void) | null = null
    private currentReject: ((reason: any) => void) | null = null

    start(url: string, options: SseOptions = {}): Promise<any> {
        const {
            successMsg = '操作成功',
            failMsg = '操作失败',
            onMessage,
            onError,
            onOpen
        } = options

        // 关闭之前的连接
        this.close()

        this.es = new EventSource(`${baseUrl}/${url}`)

        return new Promise((resolve, reject) => {
            if (!this.es) return reject(new Error('EventSource创建失败'))

            // 保存当前的 resolve 和 reject 函数
            this.currentResolve = resolve
            this.currentReject = reject

            // 绑定事件处理器
            this.es.addEventListener('open', this.createOpenHandler(onOpen))
            this.es.addEventListener('message', this.createMessageHandler(options, successMsg, failMsg))
            this.es.addEventListener('error', this.createErrorHandler(onError, failMsg))
        })
    }

    // 创建 open 事件处理器
    private createOpenHandler(onOpen?: (event: Event) => void) {
        return (event: Event) => {
            onOpen?.(event)
        }
    }

    // 创建 message 事件处理器
    private createMessageHandler(options: SseOptions, successMsg: string, failMsg: string) {
        return (event: MessageEvent) => {
            try {
                // 过滤心跳消息
                if (this.isHeartbeat(event.data)) {
                    if (options.onMessage) {
                        options.onMessage({})
                    }
                    return
                }

                const data = JSON.parse(event.data)

                // 自定义消息处理
                if (options.onMessage) {
                    options.onMessage(data)

                    // 检查是否为完成状态，如果是则自动关闭连接
                    if (data.status === 'completed' || data.status === 'syncCompleted') {
                        this.currentResolve?.(data)
                        this.close()
                    } else if (data.status === 'failed' || data.status === 'error') {
                        this.currentReject?.(data)
                        this.close()
                    }
                    return
                }

                // 默认处理逻辑
                if (data.status === 'completed' || data.status === 'syncCompleted') {
                    feedback.msgSuccess(successMsg)
                    this.currentResolve?.(data)
                    this.close()
                } else if (data.status === 'failed' || data.status === 'error') {
                    feedback.msgError(data.message || failMsg)
                    this.currentReject?.(data)
                    this.close()
                }
            } catch (error) {
                // 如果JSON解析失败，检查是否为心跳消息
                if (this.isHeartbeat(event.data)) {
                    return
                }
                console.warn('SSE消息解析失败:', event.data, error)
                // 不因为心跳消息解析失败而中断连接
            }
        }
    }

    // 创建 error 事件处理器
    private createErrorHandler(onError?: (error: Event) => void, failMsg?: string) {
        return (event: Event) => {
            if (onError) {
                onError(event)
            } else {
                feedback.msgError(failMsg || '操作失败')
            }
            this.currentReject?.(event)
            this.close()
        }
    }

    // 判断是否为心跳消息
    private isHeartbeat(data: string): boolean {
        // 常见的心跳消息格式
        const heartbeatPatterns = [
            /^:\s*$/, // ": "
            /^:\n\n$/, // ":\n\n"
            /^:\s*\n\s*$/, // ": \n "
            /^ping$/i, // "ping"
            /^heartbeat$/i, // "heartbeat"
            /^\s*$/ // 空白字符
        ]

        return heartbeatPatterns.some((pattern) => pattern.test(data))
    }

    close() {
        if (this.es) {
            // EventSource 会在 close() 时自动清理事件监听器
            this.es.close()
            this.es = null
        }
        // 清理 Promise 引用
        this.currentResolve = null
        this.currentReject = null
    }

    // 强制关闭连接（用于清理）
    forceClose() {
        if (this.es) {
            try {
                this.es.close()
            } catch (error) {
                console.warn('强制关闭SSE连接时出错:', error)
            } finally {
                this.es = null
            }
        }
    }

    // 获取当前连接状态
    get readyState() {
        return this.es?.readyState
    }

    // 检查是否连接中
    get isConnected() {
        return this.es?.readyState === EventSource.OPEN
    }

    // 检查连接是否已关闭
    get isClosed() {
        return !this.es || this.es.readyState === EventSource.CLOSED
    }
}

// 创建默认实例
const sseRequest = new Sse()

export default sseRequest
