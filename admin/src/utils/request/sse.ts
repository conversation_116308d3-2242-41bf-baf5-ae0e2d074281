import config from '@/config'
import feedback from '@/utils/feedback'

const baseUrl = config.baseUrl + config.urlPrefix

export interface SseOptions {
    successMsg?: string
    failMsg?: string
    onMessage?: (data: any) => void
    onError?: (error: Event) => void
    onOpen?: (event: Event) => void
}

export class Sse {
    private es: EventSource | null = null

    start(url: string, options: SseOptions = {}): Promise<any> {
        const {
            successMsg = '操作成功',
            failMsg = '操作失败',
            onMessage,
            onError,
            onOpen
        } = options

        // 关闭之前的连接
        this.close()

        this.es = new EventSource(`${baseUrl}/${url}`)

        return new Promise((resolve, reject) => {
            if (!this.es) return reject(new Error('EventSource创建失败'))

            this.es.addEventListener('open', (event) => {
                onOpen?.(event)
            })

            this.es.addEventListener('message', (event) => {
                try {
                    // 过滤心跳消息
                    if (this.isHeartbeat(event.data)) {
                        if (onMessage) {
                            console.log('222')
                            onMessage({})
                        }
                        return
                    }

                    const data = JSON.parse(event.data)

                    // 自定义消息处理
                    if (onMessage) {
                        onMessage(data)
                        return
                    }

                    // 默认处理逻辑
                    if (data.status === 'completed' || data.status === 'syncCompleted') {
                        feedback.msgSuccess(successMsg)
                        resolve(data)
                        this.close()
                    } else if (data.status === 'failed' || data.status === 'error') {
                        feedback.msgError(data.message || failMsg)
                        reject(data)
                        this.close()
                    }
                } catch (error) {
                    // 如果JSON解析失败，检查是否为心跳消息
                    if (this.isHeartbeat(event.data)) {
                        return
                    }
                    console.warn('SSE消息解析失败:', event.data, error)
                    // 不因为心跳消息解析失败而中断连接
                }
            })

            this.es.addEventListener('error', (event) => {
                if (onError) {
                    onError(event)
                } else {
                    feedback.msgError(failMsg)
                }
                reject(event)
                this.close()
            })
        })
    }

    // 判断是否为心跳消息
    private isHeartbeat(data: string): boolean {
        // 常见的心跳消息格式
        const heartbeatPatterns = [
            /^:\s*$/, // ": "
            /^:\n\n$/, // ":\n\n"
            /^:\s*\n\s*$/, // ": \n "
            /^ping$/i, // "ping"
            /^heartbeat$/i, // "heartbeat"
            /^\s*$/ // 空白字符
        ]

        return heartbeatPatterns.some((pattern) => pattern.test(data))
    }

    close() {
        if (this.es) {
            this.es.close()
            this.es = null
        }
    }

    // 获取当前连接状态
    get readyState() {
        return this.es?.readyState
    }

    // 检查是否连接中
    get isConnected() {
        return this.es?.readyState === EventSource.OPEN
    }
}

// 创建默认实例
const sseRequest = new Sse()

export default sseRequest
